"""CodeOCR API server for converting code screenshots to editable code."""

from pathlib import Path

import instructor
import uvicorn
from fastapi import FastAP<PERSON>, Form
from fastapi.middleware.cors import CORSMiddleware
from instructor.multimodal import Image
from openai import AsyncOpenAI
from pydantic import BaseModel, Field, field_validator


class ImageInput(BaseModel):
    """Input model for image data supporting multiple image source formats.

    This model validates and structures the incoming image data for processing
    by the code extraction service. The source field supports various formats
    that can be automatically detected by the instructor library.

    Attributes:
        source: Image source that can be a URL, file path, or base64-encoded string.
                The instructor library's Image.autodetect() method automatically
                determines the format and processes accordingly.
    """

    source: str | Path = Field(
        ...,
        description=(
            "Image source supporting URL, file path, or base64-encoded content "
            "for code extraction."
        ),
    )


class CodeOCRResponse(BaseModel):
    """Response model for code extraction results.

    This model structures the output from the code extraction service,
    providing both the extracted code content and the automatically
    detected programming language for proper syntax highlighting.

    Attributes:
        code: The extracted code content from the image, preserving
              original formatting and structure as much as possible.
        language: The detected programming language (e.g., 'python', 'javascript').
                  Automatically normalized to lowercase for consistency.
    """

    code: str = Field(
        ...,
        description="Extracted code content from the image with preserved formatting.",
    )
    language: str = Field(
        ...,
        description="Detected programming language (normalized to lowercase).",
    )

    @field_validator("language")
    @classmethod
    def lowercase_language(cls, v: str) -> str:
        return v.lower()


# Initialize FastAPI application with metadata
app = FastAPI(
    title="CodeOCR API",
    description="Convert code screenshot to editable code.",
)

# Configure CORS middleware for cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Health check endpoint."""
    return {"status": "ok", "message": "CodeOCR API is running successfully!"}


@app.post("/api/v1/codeocr", response_model=CodeOCRResponse)
async def codeocr(
    image_source: str = Form(..., description="Base64 encoded image data"),
    api_key: str = Form(..., description="API key for OpenAI"),
    api_base: str = Form("https://api.openai.com/v1", description="API base URL"),
    model_name: str = Form(..., description="Model name to use"),
) -> CodeOCRResponse:
    """Extract code from an image using a vision model.

    This endpoint processes images containing code from base64-encoded data
    and uses LLM's vision capabilities to extract the code content and
    automatically detect the programming language. The service leverages
    structured output generation to ensure consistent response formatting.

    Args:
        image_source: Base64 encoded image data.
        api_key: API key for OpenAI configuration.
        api_base: API base URL for OpenAI configuration.
        model_name: Model name to use for code extraction.

    Returns:
        CodeOCRResponse containing the extracted code and detected programming language.

    Raises:
        HTTPException: If the image processing fails or the model cannot extract code.
        ValidationError: If the input image format is invalid.
    """
    # Create OpenAI client with provided configuration
    openai_client = AsyncOpenAI(
        api_key=api_key,
        base_url=api_base,
    )

    # Create instructor client for structured output generation
    instructor_client = instructor.from_openai(
        openai_client,
        mode=instructor.Mode.JSON,
    )

    response = await instructor_client.chat.completions.create(
        model=model_name,
        response_model=CodeOCRResponse,
        messages=[
            {
                "role": "user",
                "content": [
                    "Extract code from this image. Keep the original code formatting.",
                    Image.autodetect(image_source),
                ],
            },
        ],
    )
    return response


if __name__ == "__main__":
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)
